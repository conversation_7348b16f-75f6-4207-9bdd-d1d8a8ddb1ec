// frontend/src/components/LeftNav.js
import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom'; // Import useNavigate and useLocation
import {
  FaBrain, FaFilePowerpoint, FaTable, FaComments, FaImage, FaVideo, FaThLarge, FaCloud, FaUserCircle, FaCog
} from 'react-icons/fa'; // 移除未使用的图标
import Logo from './Logo';
import ChatHistoryList from './ChatHistoryList';

const LeftNav = ({ isTaskListOpen, onToggleTaskList, onSelectChat }) => {
  const [activeItem, setActiveItem] = useState('');
  const [currentChatId, setCurrentChatId] = useState(null);
  const navigate = useNavigate(); // Initialize useNavigate
  const location = useLocation(); // Get current location

  const navItems = [
    // { id: '首页', name: '首页', icon: <FaHome size={20} />, path: '/' }, // Removed, main app handles project history
    { id: '超级智能体', name: '超级智能体', icon: <FaBrain size={20} /> },
    { id: 'AI幻灯片', name: 'AI幻灯片', icon: <FaFilePowerpoint size={20} />, path: '/' }, // Updated path to root
    { id: 'AI聊天', name: 'AI聊天', icon: <FaComments size={20} />, path: '/chat' }, // Updated path
    { id: 'AI表格', name: 'AI表格', icon: <FaTable size={20} /> },
    { id: '图片工作室', name: '图片工作室', icon: <FaImage size={20} /> },
    { id: '视频生成', name: '视频生成', icon: <FaVideo size={20} /> },
    { id: '所有智能体', name: '所有智能体', icon: <FaThLarge size={20} /> },
    { id: 'AI云盘', name: 'AI 云盘', icon: <FaCloud size={20} /> },
    { id: '我', name: '我', icon: <FaUserCircle size={20} /> },
  ];

  // 根据当前路径设置活动项
  useEffect(() => {
    const currentPath = location.pathname;
    const currentItem = navItems.find(item => item.path === currentPath);
    if (currentItem) {
      setActiveItem(currentItem.id);
    } else if (currentPath === '/') {
      setActiveItem('AI幻灯片');
    }
  }, [location.pathname]);

  const handleNavigate = (path, itemId) => {
    setActiveItem(itemId);
    navigate(path);
  };

  const handleSelectChat = (chatId) => {
    setCurrentChatId(chatId);
    onSelectChat?.(chatId);
  };

  const tasks = [
    { id: 1, name: '中国房地产市场2025年趋势分析' },
    { id: 2, name: '季度营销报告' },
  ];

  return (
    <div className="h-screen flex flex-col bg-white border-r border-gray-200 w-60">
      {/* Task List Overlay - Conditional Rendering */}
      {isTaskListOpen && (
        <div className="absolute top-0 left-0 w-60 h-full bg-white shadow-xl z-30 p-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold text-gray-700">历史任务</h2>
            <button onClick={onToggleTaskList} className="text-gray-500 hover:text-gray-700">
              ✕
            </button>
          </div>
          <ul>
            {tasks.map(task => (
              <li key={task.id} className="p-2 hover:bg-gray-100 rounded cursor-pointer text-sm text-gray-600">
                {task.name}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Main Left Navigation */}
      <div className="flex items-center h-16 px-4 border-b border-gray-200">
        {/* TikTodo Logo - Clickable to navigate to home page */}
        <div className="flex items-center">
          <a href="/" className="flex items-center" onClick={(e) => { e.preventDefault(); navigate('/'); }}>
            <Logo size={32} />
            <span className="font-bold text-2xl text-gray-800 ml-2">TikTodo</span>
          </a>
        </div>
      </div>
      <nav className="flex-grow p-2 space-y-1 custom-scrollbar overflow-y-auto">
        {navItems.map((item) => (
          <a
            key={item.id}
            href={item.path || '#'} // Use item.path if available, otherwise '#'
            onClick={(e) => {
                e.preventDefault(); // Prevent default link behavior
                if (item.path) {
                    handleNavigate(item.path, item.id);
                } else {
                    setActiveItem(item.id); // For items without a path
                }
            }}
            className={`flex items-center px-3 py-2.5 rounded-md text-sm font-medium transition-colors duration-150
              ${
                activeItem === item.id
                  ? 'bg-tiktodo-blue-light text-tiktodo-blue'
                  : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
              }
            `}
          >
            <span className="mr-3">{item.icon}</span>
            {item.name}
          </a>
        ))}
        
        {/* 在导航和聊天历史记录之间添加分割线 */}
        <div className="border-t border-gray-200 my-2"></div>
        
        {/* 在首页和聊天页面都显示历史记录 */}
        {(location.pathname === '/chat' || location.pathname === '/') && (
          <ChatHistoryList 
            currentChatId={currentChatId}
            onSelectChat={handleSelectChat}
          />
        )}
      </nav>
      <div className="p-4 border-t border-gray-200">
        <button
          className="flex items-center px-3 py-2.5 rounded-md text-sm font-medium text-gray-600 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-150 w-full"
        >
          <FaCog size={20} className="mr-3" />
          设置
        </button>
      </div>
    </div>
  );
};

export default LeftNav; 