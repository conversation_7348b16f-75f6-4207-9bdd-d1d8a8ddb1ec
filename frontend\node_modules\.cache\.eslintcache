[{"E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\index.js": "1", "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\App.js": "2", "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\RightPane.js": "3", "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\MiddlePane.js": "4", "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\LeftNav.js": "5", "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\InlineTextEditor.js": "6", "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\FullScreenPlayer.js": "7", "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\utils\\formatDate.js": "8", "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\services\\api.js": "9", "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\views\\ChatViewPage.js": "10", "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\views\\SlidePlayerView.js": "11", "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\SlideRenderer.js": "12", "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\MessageInput.js": "13", "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\SlideThumbnails.js": "14", "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\ChatMessage.js": "15", "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\Logo.js": "16", "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\ChatHistoryList.js": "17", "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\views\\ChatView.js": "18", "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\config.js": "19", "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\PlayerToolbar.js": "20", "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\PdfExport.js": "21", "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\UploadedFiles.js": "22", "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\FileUpload.js": "23"}, {"size": 982, "mtime": 1748439604030, "results": "24", "hashOfConfig": "25"}, {"size": 66338, "mtime": 1750263844732, "results": "26", "hashOfConfig": "25"}, {"size": 18700, "mtime": 1750137109622, "results": "27", "hashOfConfig": "25"}, {"size": 4734, "mtime": 1750160601114, "results": "28", "hashOfConfig": "25"}, {"size": 5498, "mtime": 1750263878402, "results": "29", "hashOfConfig": "25"}, {"size": 3908, "mtime": 1750136796975, "results": "30", "hashOfConfig": "25"}, {"size": 6387, "mtime": 1749225050456, "results": "31", "hashOfConfig": "25"}, {"size": 1208, "mtime": 1748439194549, "results": "32", "hashOfConfig": "25"}, {"size": 16052, "mtime": 1750169098352, "results": "33", "hashOfConfig": "25"}, {"size": 2893, "mtime": 1750210393578, "results": "34", "hashOfConfig": "25"}, {"size": 7460, "mtime": 1748441145339, "results": "35", "hashOfConfig": "25"}, {"size": 18567, "mtime": 1750176115267, "results": "36", "hashOfConfig": "25"}, {"size": 4632, "mtime": 1750137135894, "results": "37", "hashOfConfig": "25"}, {"size": 3041, "mtime": 1749196331208, "results": "38", "hashOfConfig": "25"}, {"size": 3500, "mtime": 1750076021369, "results": "39", "hashOfConfig": "25"}, {"size": 720, "mtime": 1748682582467, "results": "40", "hashOfConfig": "25"}, {"size": 10417, "mtime": 1750210431481, "results": "41", "hashOfConfig": "25"}, {"size": 9685, "mtime": 1750210410795, "results": "42", "hashOfConfig": "25"}, {"size": 1658, "mtime": 1748675587359, "results": "43", "hashOfConfig": "25"}, {"size": 5261, "mtime": 1749044170559, "results": "44", "hashOfConfig": "25"}, {"size": 5427, "mtime": 1748318652050, "results": "45", "hashOfConfig": "25"}, {"size": 3826, "mtime": 1750075712571, "results": "46", "hashOfConfig": "25"}, {"size": 5167, "mtime": 1750075688187, "results": "47", "hashOfConfig": "25"}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1k34gnr", {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\index.js", [], [], "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\App.js", ["117", "118", "119", "120"], [], "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\RightPane.js", ["121"], [], "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\MiddlePane.js", ["122", "123"], [], "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\LeftNav.js", ["124"], [], "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\InlineTextEditor.js", [], [], "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\FullScreenPlayer.js", [], [], "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\utils\\formatDate.js", [], [], "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\services\\api.js", [], [], "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\views\\ChatViewPage.js", ["125"], [], "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\views\\SlidePlayerView.js", ["126", "127", "128", "129", "130"], [], "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\SlideRenderer.js", [], [], "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\MessageInput.js", ["131", "132", "133", "134"], [], "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\SlideThumbnails.js", [], [], "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\ChatMessage.js", [], [], "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\Logo.js", [], [], "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\ChatHistoryList.js", ["135"], [], "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\views\\ChatView.js", ["136"], [], "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\config.js", [], [], "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\PlayerToolbar.js", ["137", "138", "139", "140", "141"], [], "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\PdfExport.js", [], [], "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\UploadedFiles.js", [], [], "E:\\cursor\\ppt\\TikTodo-aippt\\frontend\\src\\components\\FileUpload.js", [], [], {"ruleId": "142", "severity": 1, "message": "143", "line": 22, "column": 10, "nodeType": "144", "messageId": "145", "endLine": 22, "endColumn": 26}, {"ruleId": "142", "severity": 1, "message": "146", "line": 45, "column": 10, "nodeType": "144", "messageId": "145", "endLine": 45, "endColumn": 27}, {"ruleId": "142", "severity": 1, "message": "147", "line": 436, "column": 9, "nodeType": "144", "messageId": "145", "endLine": 436, "endColumn": 28}, {"ruleId": "142", "severity": 1, "message": "148", "line": 1297, "column": 9, "nodeType": "144", "messageId": "145", "endLine": 1297, "endColumn": 33}, {"ruleId": "142", "severity": 1, "message": "149", "line": 381, "column": 9, "nodeType": "144", "messageId": "145", "endLine": 381, "endColumn": 18}, {"ruleId": "150", "severity": 1, "message": "151", "line": 38, "column": 9, "nodeType": "152", "endLine": 45, "endColumn": 4, "suggestions": "153"}, {"ruleId": "142", "severity": 1, "message": "154", "line": 63, "column": 9, "nodeType": "144", "messageId": "145", "endLine": 63, "endColumn": 23}, {"ruleId": "150", "severity": 1, "message": "155", "line": 38, "column": 6, "nodeType": "156", "endLine": 38, "endColumn": 25, "suggestions": "157"}, {"ruleId": "142", "severity": 1, "message": "158", "line": 11, "column": 10, "nodeType": "144", "messageId": "145", "endLine": 11, "endColumn": 19}, {"ruleId": "142", "severity": 1, "message": "159", "line": 6, "column": 10, "nodeType": "144", "messageId": "145", "endLine": 6, "endColumn": 21}, {"ruleId": "142", "severity": 1, "message": "160", "line": 6, "column": 23, "nodeType": "144", "messageId": "145", "endLine": 6, "endColumn": 35}, {"ruleId": "142", "severity": 1, "message": "161", "line": 6, "column": 37, "nodeType": "144", "messageId": "145", "endLine": 6, "endColumn": 44}, {"ruleId": "150", "severity": 1, "message": "162", "line": 61, "column": 6, "nodeType": "156", "endLine": 61, "endColumn": 40, "suggestions": "163"}, {"ruleId": "142", "severity": 1, "message": "164", "line": 147, "column": 9, "nodeType": "144", "messageId": "145", "endLine": 147, "endColumn": 21}, {"ruleId": "142", "severity": 1, "message": "165", "line": 16, "column": 29, "nodeType": "144", "messageId": "145", "endLine": 16, "endColumn": 49}, {"ruleId": "142", "severity": 1, "message": "166", "line": 20, "column": 9, "nodeType": "144", "messageId": "145", "endLine": 20, "endColumn": 22}, {"ruleId": "142", "severity": 1, "message": "167", "line": 21, "column": 9, "nodeType": "144", "messageId": "145", "endLine": 21, "endColumn": 27}, {"ruleId": "142", "severity": 1, "message": "168", "line": 22, "column": 9, "nodeType": "144", "messageId": "145", "endLine": 22, "endColumn": 22}, {"ruleId": "142", "severity": 1, "message": "169", "line": 74, "column": 9, "nodeType": "144", "messageId": "145", "endLine": 74, "endColumn": 24}, {"ruleId": "142", "severity": 1, "message": "170", "line": 6, "column": 18, "nodeType": "144", "messageId": "145", "endLine": 6, "endColumn": 28}, {"ruleId": "142", "severity": 1, "message": "171", "line": 3, "column": 31, "nodeType": "144", "messageId": "145", "endLine": 3, "endColumn": 41}, {"ruleId": "142", "severity": 1, "message": "172", "line": 34, "column": 9, "nodeType": "144", "messageId": "145", "endLine": 34, "endColumn": 27}, {"ruleId": "173", "severity": 1, "message": "174", "line": 82, "column": 17, "nodeType": "175", "endLine": 87, "endColumn": 18}, {"ruleId": "173", "severity": 1, "message": "174", "line": 109, "column": 17, "nodeType": "175", "endLine": 114, "endColumn": 18}, {"ruleId": "173", "severity": 1, "message": "174", "line": 135, "column": 17, "nodeType": "175", "endLine": 140, "endColumn": 18}, "no-unused-vars", "'isLoadingHistory' is assigned a value but never used.", "Identifier", "unusedVar", "'showHistoryDrawer' is assigned a value but never used.", "'handleSelectHistory' is assigned a value but never used.", "'handleCloseHistoryDrawer' is assigned a value but never used.", "'titleText' is assigned a value but never used.", "react-hooks/exhaustive-deps", "The 'handleTitleEditComplete' function makes the dependencies of useEffect Hook (at line 60) change on every render. To fix this, wrap the definition of 'handleTitleEditComplete' in its own useCallback() Hook.", "VariableDeclarator", ["176"], "'handleToolView' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'navItems'. Either include it or remove the dependency array.", "ArrayExpression", ["177"], "'isLoading' is assigned a value but never used.", "'FaArrowLeft' is defined but never used.", "'FaArrowRight' is defined but never used.", "'FaTimes' is defined but never used.", "React Hook useEffect has a missing dependency: 'handleExitPresentation'. Either include it or remove the dependency array.", ["178"], "'currentSlide' is assigned a value but never used.", "'setIsManuallyResized' is assigned a value but never used.", "'dragStartYRef' is assigned a value but never used.", "'dragStartHeightRef' is assigned a value but never used.", "'isDraggingRef' is assigned a value but never used.", "'formatTimestamp' is assigned a value but never used.", "'FaComments' is defined but never used.", "'FaShareAlt' is defined but never used.", "'handleClickOutside' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", {"desc": "179", "fix": "180"}, {"desc": "181", "fix": "182"}, {"desc": "183", "fix": "184"}, "Wrap the definition of 'handleTitleEditComplete' in its own useCallback() Hook.", {"range": "185", "text": "186"}, "Update the dependencies array to be: [location.pathname, navItems]", {"range": "187", "text": "188"}, "Update the dependencies array to be: [handleExitPresentation, handleNextSlide, handlePrevSlide]", {"range": "189", "text": "190"}, [976, 1177], "useCallback(() => {\n    setIsEditingTitle(false);\n    if (editableTitle.trim() !== '' && editableTitle !== title) {\n      onTitleClick(editableTitle);\n    } else {\n      setEditableTitle(title || '新对话');\n    }\n  })", [1817, 1836], "[location.pathname, navItems]", [2134, 2168], "[handleExitPresentation, handleNextSlide, handlePrevSlide]"]